/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.CORE_TRANSFERS_TDS_ENABLED;
import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;
import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest
@AutoConfigureMockMvc
public class CoreTransfersActivityIntegrationTest {

  @Autowired private MockMvc mockMvc;

  @MockBean FeatureFlagService featureFlagService;
  @MockBean CoreTransfersClient coreTransfersClient;
  @MockBean CesClient cesClient;

  static final String BEARER_TOKEN =
      "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA";

  @Test
  @DisplayName("Create immediate core transfer and verify activity retrieval - Success scenario")
  void createImmediateCoreTransferAndGetActivity_Success() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse = createSuccessfulTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(transferResponse);

    // Create immediate transfer
    var transferContent = createImmediateTransferRequest();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.activity.activityType").value("INTERNAL_TRANSFER"))
        .andExpect(jsonPath("$.activity.displayStatus").value("Processed"))
        .andExpect(jsonPath("$.activity.amount").value(100.50));

    // Mock activity retrieval with both CES and Core Transfers data
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    TDSCoreTransferActivityResponse coreTransfersActivityResponse =
        createCoreTransfersActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersActivityResponse);

    // Verify activity retrieval includes the created transfer
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray())
        .andExpect(jsonPath("$.recentActivities[?(@.amount == 100.50)]").exists())
        .andExpect(jsonPath("$.recentActivities[?(@.displayStatus == 'Completed')]").exists());
  }

  @Test
  @DisplayName("Core transfers enabled but CES fails - Should return PARTIAL_SUCCESS")
  void getActivity_CesFailsCoreTransfersSucceeds_ReturnsPartialSuccess() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock CES failure
    when(cesClient.getTransferAndPayActivity())
        .thenThrow(new RuntimeException("CES service unavailable"));

    // Mock successful Core Transfers response
    TDSCoreTransferActivityResponse coreTransfersResponse = createCoreTransfersActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("PARTIAL_SUCCESS"))
        .andExpect(jsonPath("$.retrievalErrors").isArray())
        .andExpect(jsonPath("$.retrievalErrors[0]").value("UNABLE_TO_GET_ACTIVITY"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.recentActivities[?(@.amount == 100.50)]").exists());
  }

  @Test
  @DisplayName("Core transfers fails but CES succeeds - Should return PARTIAL_SUCCESS")
  void getActivity_CoreTransfersFailsCesSucceeds_ReturnsPartialSuccess() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    // Mock Core Transfers failure
    when(coreTransfersClient.getTransferActivity())
        .thenThrow(new RuntimeException("Core Transfers service unavailable"));

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("PARTIAL_SUCCESS"))
        .andExpect(jsonPath("$.retrievalErrors").isArray())
        .andExpect(jsonPath("$.retrievalErrors[0]").value("RETRIEVAL_ERROR_CORE_TRANSFERS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray());
  }

  @Test
  @DisplayName("Both CES and Core Transfers fail - Should throw service unavailable exception")
  void getActivity_BothServicesFailure_ThrowsServiceUnavailable() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock both services failing
    when(cesClient.getTransferAndPayActivity())
        .thenThrow(new RuntimeException("CES service unavailable"));
    when(coreTransfersClient.getTransferActivity())
        .thenThrow(new RuntimeException("Core Transfers service unavailable"));

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andExpect(jsonPath("$.status").value("SERVICE_UNAVAILABLE"));
  }

  @Test
  @DisplayName("Core transfers disabled - Should only use CES")
  void getActivity_CoreTransfersDisabled_OnlyUsesCes() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(false);

    // Mock successful CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray());

    // Verify Core Transfers client was never called
    verify(coreTransfersClient, never()).getTransferActivity();
  }

  @Test
  @DisplayName("Activity limits applied correctly with mixed CES and Core Transfers data")
  void getActivity_WithLimits_AppliesLimitsCorrectly() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock responses with multiple activities
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    TDSCoreTransferActivityResponse coreTransfersResponse = createMultipleCoreTransfersActivities();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .param("recentLimit", "2")
                .param("upcomingLimit", "3")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.recentActivities.length()").value(2))
        .andExpect(jsonPath("$.upcomingActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities.length()").value(3))
        .andExpect(jsonPath("$.recentTruncated").value(true))
        .andExpect(jsonPath("$.upcomingTruncated").value(true));
  }

  @Test
  @DisplayName("Core transfer with different statuses - Success, Failed, Pending")
  void getActivity_DifferentTransferStatuses_MapsCorrectly() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    // Mock Core Transfers response with different statuses
    TDSCoreTransferActivityResponse coreTransfersResponse = createActivitiesWithDifferentStatuses();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities[?(@.displayStatus == 'Completed')]").exists())
        .andExpect(jsonPath("$.recentActivities[?(@.displayStatus == 'Unsuccessful')]").exists())
        .andExpect(jsonPath("$.upcomingActivities[?(@.displayStatus == 'Scheduled')]").exists());
  }

  @Test
  @DisplayName("Create core transfer with error response")
  void createCoreTransfer_WithError_ReturnsError() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock error response from core transfers
    TDSCoreTransferResponse errorResponse = createErrorTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(errorResponse);

    var transferContent = createImmediateTransferRequest();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.status").value("ERROR"));
  }

  @Test
  @DisplayName("Empty core transfers activity response")
  void getActivity_EmptyCoreTransfersResponse_HandlesGracefully() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    // Mock empty Core Transfers response
    TDSCoreTransferActivityResponse emptyResponse = new TDSCoreTransferActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(emptyResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray());
  }

  // Helper methods for creating test data
  private String createImmediateTransferRequest() {
    return
        """
        {
            "amount": 100.50,
            "dueDate": "%s",
            "frequency": "ONE_TIME",
            "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
            "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
            "memo": "Test immediate transfer",
            "requestGuid": "267512b1-c64e-4219-ba36-1be9b6a28811",
            "scheduleImmediately": true,
            "activityType": "INTERNAL_TRANSFER",
            "fromAccountType": "DDA",
            "toAccountType": "SAV"
        }
        """
        .formatted(LocalDate.now());
  }

  private TDSCoreTransferResponse createSuccessfulTransferResponse() {
    TDSCoreTransferResponse response = new TDSCoreTransferResponse();
    response.setReferenceId("REF-12345");
    return response;
  }

  private TDSCoreTransferActivityResponse createCoreTransfersActivityResponse() {
    TDSCoreTransferActivity activity = new TDSCoreTransferActivity();
    activity.setReferenceId("REF-12345");
    activity.setFromAccountId("abf3228b-5066-4729-8e15-b7d44afc0a8a");
    activity.setFromAccountType("DDA");
    activity.setToAccountId("8a793e2a-21cc-4d50-af8a-76cab53935a2");
    activity.setToAccountType("SAV");
    activity.setAmount(new BigDecimal("100.50"));
    activity.setTransferStatus("SUCCESS");
    activity.setCreatedDate(LocalDate.now());
    activity.setExpectedPostingDate(LocalDate.now());

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(activity));
    return response;
  }

  private TDSCoreTransferActivityResponse createMultipleCoreTransfersActivities() {
    // Create completed activity
    TDSCoreTransferActivity completedActivity = new TDSCoreTransferActivity();
    completedActivity.setReferenceId("REF-COMPLETED-1");
    completedActivity.setFromAccountId("account-1");
    completedActivity.setToAccountId("account-2");
    completedActivity.setAmount(new BigDecimal("50.00"));
    completedActivity.setTransferStatus("SUCCESS");
    completedActivity.setCreatedDate(LocalDate.now().minusDays(1));
    completedActivity.setExpectedPostingDate(LocalDate.now().minusDays(1));

    // Create pending activity
    TDSCoreTransferActivity pendingActivity = new TDSCoreTransferActivity();
    pendingActivity.setReferenceId("REF-PENDING-1");
    pendingActivity.setFromAccountId("account-3");
    pendingActivity.setToAccountId("account-4");
    pendingActivity.setAmount(new BigDecimal("75.00"));
    pendingActivity.setTransferStatus("PENDING");
    pendingActivity.setCreatedDate(LocalDate.now());
    pendingActivity.setExpectedPostingDate(LocalDate.now().plusDays(1));

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(completedActivity, pendingActivity));
    return response;
  }

  private TDSCoreTransferActivityResponse createActivitiesWithDifferentStatuses() {
    // Create successful/completed activity
    TDSCoreTransferActivity successActivity = new TDSCoreTransferActivity();
    successActivity.setReferenceId("REF-SUCCESS");
    successActivity.setFromAccountId("account-1");
    successActivity.setToAccountId("account-2");
    successActivity.setAmount(new BigDecimal("100.00"));
    successActivity.setTransferStatus("SUCCESS");
    successActivity.setCreatedDate(LocalDate.now().minusDays(1));
    successActivity.setExpectedPostingDate(LocalDate.now().minusDays(1));

    // Create failed activity
    TDSCoreTransferActivity failedActivity = new TDSCoreTransferActivity();
    failedActivity.setReferenceId("REF-FAILED");
    failedActivity.setFromAccountId("account-3");
    failedActivity.setToAccountId("account-4");
    failedActivity.setAmount(new BigDecimal("200.00"));
    failedActivity.setTransferStatus("INVALID_ACC_SOURCE");
    failedActivity.setCreatedDate(LocalDate.now().minusDays(2));
    failedActivity.setExpectedPostingDate(LocalDate.now().minusDays(2));

    // Create pending/scheduled activity
    TDSCoreTransferActivity pendingActivity = new TDSCoreTransferActivity();
    pendingActivity.setReferenceId("REF-PENDING");
    pendingActivity.setFromAccountId("account-5");
    pendingActivity.setToAccountId("account-6");
    pendingActivity.setAmount(new BigDecimal("150.00"));
    pendingActivity.setTransferStatus("PENDING");
    pendingActivity.setCreatedDate(LocalDate.now());
    pendingActivity.setExpectedPostingDate(LocalDate.now().plusDays(1));

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(successActivity, failedActivity, pendingActivity));
    return response;
  }

  private TDSCoreTransferResponse createErrorTransferResponse() {
    TDSCoreTransferResponse response = new TDSCoreTransferResponse();
    TDSCoreTransferResponse.ErrorResponse error = new TDSCoreTransferResponse.ErrorResponse();
    error.setCode("INSUFFICIENT_FUNDS");
    error.setMessage("Insufficient funds in source account");
    error.setTarget("fromAccountId");
    response.setError(error);
    return response;
  }
}
