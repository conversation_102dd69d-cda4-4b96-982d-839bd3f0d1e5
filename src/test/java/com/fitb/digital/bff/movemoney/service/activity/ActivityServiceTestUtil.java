/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;

import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public class ActivityServiceTestUtil {
  protected static CompletableFuture<ClientActivityResponse> mockClientActivityAsyncResponse() {
    try {
      return CompletableFuture.supplyAsync(
          () -> mockFromFile("full_activity_response.json", ClientActivityResponse.class));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  protected static CompletableFuture<ClientActivityResponse>
      mockClientActivityAsyncResponseWithNullDueDate() {
    try {
      return CompletableFuture.supplyAsync(
          () -> mockFromFile("activity_null_due_date.json", ClientActivityResponse.class));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  protected static CompletableFuture<ClientActivityResponse> mockEmptyActivityAsyncResponse() {
    return futureFromClientActivityResponse(
        mockFromFile("empty_activity_response.json", ClientActivityResponse.class));
  }

  protected static CompletableFuture<ClientActivityResponse> futureFromClientActivityResponse(
      ClientActivityResponse response) {
    try {
      return CompletableFuture.supplyAsync(() -> response);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /** Mock Core Transfers activities - returns 2 recent (completed) and 3 upcoming (scheduled) */
  protected static CompletableFuture<List<BffActivity>> mockCoreTransfersActivitiesAsync() {
    List<BffActivity> activities = new ArrayList<>();

    // Add 2 recent (completed) activities
    BffActivity recentActivity1 =
        createCoreTransferActivity(
            "ct-1", ActivityBase.COMPLETED_STATUS, LocalDate.now().minusDays(1));
    BffActivity recentActivity2 =
        createCoreTransferActivity(
            "ct-2", ActivityBase.UNSUCCESSFUL_STATUS, LocalDate.now().minusDays(2));
    activities.add(recentActivity1);
    activities.add(recentActivity2);

    // Add 3 upcoming (scheduled) activities
    BffActivity upcomingActivity1 =
        createCoreTransferActivity(
            "ct-3", ActivityBase.SCHEDULED_STATUS, LocalDate.now().plusDays(1));
    BffActivity upcomingActivity2 =
        createCoreTransferActivity(
            "ct-4", ActivityBase.IN_PROCESS_STATUS, LocalDate.now().plusDays(2));
    BffActivity upcomingActivity3 =
        createCoreTransferActivity(
            "ct-5", ActivityBase.SCHEDULED_STATUS, LocalDate.now().plusDays(3));
    activities.add(upcomingActivity1);
    activities.add(upcomingActivity2);
    activities.add(upcomingActivity3);

    return CompletableFuture.completedFuture(activities);
  }

  /** Mock empty Core Transfers activities */
  protected static CompletableFuture<List<BffActivity>> mockEmptyCoreTransfersActivitiesAsync() {
    return CompletableFuture.completedFuture(new ArrayList<>());
  }

  /** Mock Core Transfers activities that throws exception */
  protected static CompletableFuture<List<BffActivity>> mockFailingCoreTransfersActivitiesAsync() {
    CompletableFuture<List<BffActivity>> future = new CompletableFuture<>();
    future.completeExceptionally(new RuntimeException("Core Transfers service unavailable"));
    return future;
  }

  private static BffActivity createCoreTransferActivity(
      String id, String status, LocalDate dueDate) {
    BffActivity activity = new BffActivity();
    activity.setId(id);
    activity.setDisplayId(id);
    activity.setDisplayStatus(status);
    activity.setDueDate(dueDate);
    activity.setCreateTimestamp(LocalDateTime.now());
    activity.setFromAccountId("core-from-" + id);
    activity.setToAccountId("core-to-" + id);
    activity.setFromAccountName("Core Transfer From Account");
    activity.setToAccountName("Core Transfer To Account");
    activity.setAmount(100.0);
    activity.setActivityType(ActivityBase.INTERNAL_TRANSFER);
    activity.setMemo("Core Transfer Activity " + id);
    return activity;
  }
}
